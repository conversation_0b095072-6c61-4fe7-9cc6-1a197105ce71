{"hash": "431e7845", "configHash": "22747e38", "lockfileHash": "61df7601", "browserHash": "b29061fb", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "868dc998", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "73d60a6a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d22060cf", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "1b4292b6", "needsInterop": true}, "@chakra-ui/icons": {"src": "../../@chakra-ui/icons/dist/esm/index.mjs", "file": "@chakra-ui_icons.js", "fileHash": "29362dde", "needsInterop": false}, "@chakra-ui/react": {"src": "../../@chakra-ui/react/dist/esm/index.mjs", "file": "@chakra-ui_react.js", "fileHash": "07d133e8", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0495f1ba", "needsInterop": true}, "react-icons/io5": {"src": "../../react-icons/io5/index.mjs", "file": "react-icons_io5.js", "fileHash": "fe6f7f09", "needsInterop": false}, "react-icons/lu": {"src": "../../react-icons/lu/index.mjs", "file": "react-icons_lu.js", "fileHash": "4e8c570e", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "c9747ea4", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "cf7271c4", "needsInterop": false}, "express": {"src": "../../../../node_modules/express/index.js", "file": "express.js", "fileHash": "d76025f1", "needsInterop": true}}, "chunks": {"chunk-C4XDDFHN": {"file": "chunk-C4XDDFHN.js"}, "chunk-XUA7OPFZ": {"file": "chunk-XUA7OPFZ.js"}, "chunk-NA32P3ZC": {"file": "chunk-NA32P3ZC.js"}, "chunk-YX33HODG": {"file": "chunk-YX33HODG.js"}, "chunk-R26XTA6N": {"file": "chunk-R26XTA6N.js"}, "chunk-PLDDJCW6": {"file": "chunk-PLDDJCW6.js"}}}