import { Container, SimpleGrid } from '@chakra-ui/react'
import { Link } from 'react-router-dom'
import { Text, VStack } from '@chakra-ui/react'
import { useEffect } from 'react';
import { useProductStore } from '../store/product';
import ProductCard from '../components/ProductCard';

const HomePage = () => {
  const {fetchProducts, product} = useProductStore();
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);
  console.log("product",product);

  return (
    <Container maxW={"container.xl"} py={12}>
      <VStack spacing={8}>
        <Text
          bgGradient={"linear(to-r,cyan.400 ,blue.500)"}
          bgClip={"text"}
          textAlign={"center"}
          fontSize={"30"}
          fontWeight={"bold"}
        >
          Current Product
        </Text>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={10} w={"full"}>
          {product.map((product) => (
            <ProductCard key={product._id} product={product} />
          ))}
        </SimpleGrid>
        {product.length === 0 && (
          <Text
            fontSize={"xl"}
            textAlign={"center"}
            color={"gray.500"}
            fontWeight={"bold"}
          >
            No Products Found {""}
            <Link to="/create">
              <Text
                as="span"
                color={"blue.500"}
                _hover={{ textDecoration: "underline" }}
              >
                Create New Product
              </Text>
            </Link>
          </Text>
        )}
      </VStack>
    </Container>
  );
}

export default HomePage