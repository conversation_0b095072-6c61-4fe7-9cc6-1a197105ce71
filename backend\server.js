import express from 'express';
import dotenv from 'dotenv';
import path from 'path';//deployment
import connectDB from './config/db.js';
import productRoutes from './routes/product.routes.js';

dotenv.config();

const app =express();
const port = 3000;

const __dirname = path.resolve();//deployment

app.use(express.json());

app.use("/api/products", productRoutes);

//deployment
if(process.env.NODE_ENV === "production"){
    app.use(express.static(path.join(__dirname, "/frontend/build")));

    app.get("*", (req, res) => {
        res.sendFile(path.resolve(__dirname, "frontend", "build", "index.html"));
    });
}



app.listen(port, () => {
    connectDB();
    console.log(`Server is running on port ${port}`);
});
