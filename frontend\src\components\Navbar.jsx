import { Container, Flex, Text, HStack, Button } from '@chakra-ui/react'
import { Link } from 'react-router-dom'
import { PlusSquareIcon } from '@chakra-ui/icons'
import { IoMoon } from 'react-icons/io5'
import { LuSun } from 'react-icons/lu'
import { useColorMode } from '@chakra-ui/react'



const Navbar = () => {
  const {colorMode ,toggleColorMode}= useColorMode()
  return (
    <Container maxW={"1140px"} py={5}>
      <Flex
        h={"16px"}
        justifyContent={"space-between"}
        alignItems={"center"}
        flexdir={{
          base: "column",
          sm: "row",
        }}
      >
      <Text
        bgGradient={"linear(to-r,rgb(93, 185, 238),rgb(66, 43, 241))"}
        bgClip={"text"}
        textTransform={"uppercase"}
        textAlign={"center"}
        fontSize={{ base: 22, sm: 28 }}
        fontWeight={"bold"}
      >
        <Link to="/">Product Store </Link>
      </Text>
      <HStack spacing={2} alignItems={"center"}>
        <Link to="/create">
          <Button>
            <PlusSquareIcon fontSize={"20"}/>
        </Button>
        </Link>
        <Button onClick={toggleColorMode}>
            {colorMode === "light" ? <IoMoon/>: <LuSun size={20}/>}
        </Button>
      </HStack>
      </Flex>
    </Container>
  );
}

export default Navbar